{% extends "base_modern.html" %}

{% block title %}Q&A - Graphiti{% endblock %}

{% block content %}
<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">Q&A Assistant</h1>
        <p class="text-muted">Ask questions about your knowledge graph</p>
    </div>
    <div class="dropdown">
        <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
            <i class="bi bi-robot"></i>
            <span id="current-model">Select Model</span>
            <span id="model-status" class="badge bg-secondary ms-2" style="display: none;"></span>
        </button>
        <ul class="dropdown-menu dropdown-menu-end" id="model-dropdown" style="max-height: 400px; overflow-y: auto; min-width: 350px;">
            <li><a class="dropdown-item" href="#"><span class="loading-spinner"></span> Loading models...</a></li>
        </ul>
    </div>
</div>

<!-- Chat Interface -->
<div class="row">
    <div class="col-12">
        <div class="card" style="height: 70vh;">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="bi bi-chat-dots"></i> Conversation</h5>
                <div>
                    <button class="btn btn-sm btn-outline-secondary me-2" onclick="clearChat()">
                        <i class="bi bi-trash"></i> Clear
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="exportChat()">
                        <i class="bi bi-download"></i> Export
                    </button>
                </div>
            </div>
            <div class="card-body d-flex flex-column p-0">
                <!-- Chat Messages -->
                <div class="flex-grow-1 overflow-auto p-3" id="chat-messages" style="max-height: calc(70vh - 140px);">
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-chat-square-dots fs-1 mb-3"></i>
                        <h5>Start a conversation</h5>
                        <p>Ask questions about your documents and knowledge graph</p>
                    </div>
                </div>

                <!-- Chat Input -->
                <div class="border-top p-3">
                    <form id="chat-form" class="d-flex gap-2">
                        <div class="flex-grow-1">
                            <textarea
                                class="form-control"
                                id="question-input"
                                placeholder="Ask a question about your documents..."
                                rows="2"
                                style="resize: none;"
                            ></textarea>
                        </div>
                        <div class="d-flex flex-column gap-2">
                            <button type="submit" class="btn btn-primary" id="send-button">
                                <i class="bi bi-send"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="toggleSettings()">
                                <i class="bi bi-gear"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Settings Panel -->
<div class="row mt-4" id="settings-panel" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-sliders"></i> Q&A Settings</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label for="temperature" class="form-label">Temperature</label>
                        <input type="range" class="form-range" id="temperature" min="0" max="1" step="0.1" value="0.7">
                        <small class="text-muted">Creativity: <span id="temperature-value">0.7</span></small>
                    </div>
                    <div class="col-md-3">
                        <label for="max-tokens" class="form-label">Max Tokens</label>
                        <input type="number" class="form-control" id="max-tokens" value="2048" min="100" max="4096">
                        <small class="text-muted">Response length limit</small>
                    </div>
                    <div class="col-md-3">
                        <label for="top-k" class="form-label">Top K</label>
                        <input type="number" class="form-control" id="top-k" value="40" min="1" max="100">
                        <small class="text-muted">Vocabulary diversity</small>
                    </div>
                    <div class="col-md-3">
                        <label for="context-limit" class="form-label">Context Limit</label>
                        <input type="number" class="form-control" id="context-limit" value="5" min="1" max="20">
                        <small class="text-muted">Number of context chunks</small>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="include-sources" checked>
                            <label class="form-check-label" for="include-sources">
                                Include source citations
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="stream-response" checked>
                            <label class="form-check-label" for="stream-response">
                                Stream responses
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Example Questions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-lightbulb"></i> Example Questions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Document Analysis</h6>
                        <ul class="list-unstyled">
                            <li><a href="#" class="text-decoration-none" onclick="askQuestion('What are the main topics discussed in the documents?')">
                                <i class="bi bi-arrow-right"></i> What are the main topics discussed?
                            </a></li>
                            <li><a href="#" class="text-decoration-none" onclick="askQuestion('Summarize the key findings from the research papers.')">
                                <i class="bi bi-arrow-right"></i> Summarize key findings
                            </a></li>
                            <li><a href="#" class="text-decoration-none" onclick="askQuestion('What methodologies are mentioned in the documents?')">
                                <i class="bi bi-arrow-right"></i> What methodologies are mentioned?
                            </a></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Entity Relationships</h6>
                        <ul class="list-unstyled">
                            <li><a href="#" class="text-decoration-none" onclick="askQuestion('How are the entities connected in the knowledge graph?')">
                                <i class="bi bi-arrow-right"></i> How are entities connected?
                            </a></li>
                            <li><a href="#" class="text-decoration-none" onclick="askQuestion('What are the most important entities mentioned?')">
                                <i class="bi bi-arrow-right"></i> Most important entities
                            </a></li>
                            <li><a href="#" class="text-decoration-none" onclick="askQuestion('Find relationships between specific concepts.')">
                                <i class="bi bi-arrow-right"></i> Find concept relationships
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let chatHistory = [];
let currentModel = null;

document.addEventListener('DOMContentLoaded', function() {
    loadAvailableModels();
    setupEventListeners();
    loadChatHistory();
});

function setupEventListeners() {
    // Chat form submission
    document.getElementById('chat-form').addEventListener('submit', handleChatSubmit);

    // Enter key handling
    document.getElementById('question-input').addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleChatSubmit(e);
        }
    });

    // Temperature slider
    document.getElementById('temperature').addEventListener('input', function() {
        document.getElementById('temperature-value').textContent = this.value;
    });
}

async function loadAvailableModels() {
    try {
        const response = await fetch('/api/settings/models');
        if (response.ok) {
            const data = await response.json();
            const dropdown = document.getElementById('model-dropdown');

            dropdown.innerHTML = '';

            // Add OpenRouter models
            if (data.available_models.openrouter && data.available_models.openrouter.length > 0) {
                // Separate free and paid models
                const freeModels = data.available_models.openrouter.filter(model =>
                    model.includes(':free') || model.includes('free')
                );
                const paidModels = data.available_models.openrouter.filter(model =>
                    !model.includes(':free') && !model.includes('free')
                );

                // Add free models section
                if (freeModels.length > 0) {
                    dropdown.innerHTML += '<li><h6 class="dropdown-header text-success">🆓 OpenRouter Free Models</h6></li>';
                    freeModels.slice(0, 15).forEach(model => {
                        dropdown.innerHTML += `
                            <li><a class="dropdown-item" href="#" onclick="selectModel('${model}', 'openrouter')">
                                <span class="badge bg-success me-2">FREE</span>${model}
                            </a></li>
                        `;
                    });
                }

                // Add paid models section
                if (paidModels.length > 0) {
                    dropdown.innerHTML += '<li><hr class="dropdown-divider"></li>';
                    dropdown.innerHTML += '<li><h6 class="dropdown-header">💰 OpenRouter Paid Models</h6></li>';
                    paidModels.slice(0, 10).forEach(model => {
                        dropdown.innerHTML += `
                            <li><a class="dropdown-item" href="#" onclick="selectModel('${model}', 'openrouter')">${model}</a></li>
                        `;
                    });
                }
            }

            // Add Ollama models
            if (data.available_models.ollama && data.available_models.ollama.length > 0) {
                dropdown.innerHTML += '<li><hr class="dropdown-divider"></li>';
                dropdown.innerHTML += '<li><h6 class="dropdown-header">Ollama</h6></li>';
                data.available_models.ollama.forEach(model => {
                    dropdown.innerHTML += `
                        <li><a class="dropdown-item" href="#" onclick="selectModel('${model}', 'ollama')">${model}</a></li>
                    `;
                });
            }

            // Set default model
            if (data.available_models.openrouter && data.available_models.openrouter.length > 0) {
                selectModel(data.available_models.openrouter[0], 'openrouter');
            }
        }
    } catch (error) {
        console.error('Error loading models:', error);
        showAlert('Error loading available models', 'warning');
    }
}

function selectModel(model, provider) {
    currentModel = { model, provider };

    // Update model display
    const modelDisplay = document.getElementById('current-model');
    const statusBadge = document.getElementById('model-status');

    // Truncate long model names for display
    const displayName = model.length > 30 ? model.substring(0, 30) + '...' : model;
    modelDisplay.textContent = displayName;

    // Show status badge
    statusBadge.style.display = 'inline';
    if (model.includes(':free') || model.includes('free')) {
        statusBadge.textContent = 'FREE';
        statusBadge.className = 'badge bg-success ms-2';
    } else if (provider === 'ollama') {
        statusBadge.textContent = 'LOCAL';
        statusBadge.className = 'badge bg-info ms-2';
    } else {
        statusBadge.textContent = 'PAID';
        statusBadge.className = 'badge bg-warning ms-2';
    }

    showAlert(`Selected model: ${model} (${provider})`, 'success', 2000);
}

async function handleChatSubmit(e) {
    e.preventDefault();

    const questionInput = document.getElementById('question-input');
    const question = questionInput.value.trim();

    if (!question) return;
    if (!currentModel) {
        showAlert('Please select a model first', 'warning');
        return;
    }

    // Clear input
    questionInput.value = '';

    // Add user message
    addMessage('user', question);

    // Show typing indicator
    const typingId = addMessage('assistant', '<div class="loading-spinner"></div> Thinking...', true);

    try {
        const response = await fetch('/api/qa/answer', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                question: question,
                llm_model: currentModel.model,
                llm_provider: currentModel.provider,
                max_facts: parseInt(document.getElementById('context-limit').value) || 10
            })
        });

        if (response.ok) {
            const data = await response.json();

            // Remove typing indicator
            removeMessage(typingId);

            // Add assistant response
            addMessage('assistant', data.answer, false, data.sources);

        } else {
            removeMessage(typingId);
            const errorText = await response.text();
            console.error('API Error:', response.status, errorText);

            let errorMessage = 'Sorry, I encountered an error processing your question.';
            if (response.status === 500) {
                errorMessage += ' The server encountered an internal error. Please try again or contact support.';
            } else if (response.status === 429) {
                errorMessage += ' Too many requests. Please wait a moment and try again.';
            } else if (response.status === 401) {
                errorMessage += ' Authentication error. Please check your API keys.';
            }

            addMessage('assistant', errorMessage);
            showAlert(`Error ${response.status}: ${errorMessage}`, 'danger', 5000);
        }

    } catch (error) {
        console.error('Error asking question:', error);
        removeMessage(typingId);

        let errorMessage = 'Sorry, I encountered an error processing your question.';
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            errorMessage += ' Unable to connect to the server. Please check your connection.';
        } else if (error.name === 'AbortError') {
            errorMessage += ' Request timed out. Please try again.';
        }

        addMessage('assistant', errorMessage);
        showAlert(`Network Error: ${error.message}`, 'danger', 5000);
    }
}

function addMessage(role, content, isTemporary = false, sources = null) {
    const messagesContainer = document.getElementById('chat-messages');
    const messageId = 'msg-' + Date.now();

    // Clear welcome message if it exists
    const welcomeMsg = messagesContainer.querySelector('.text-center.text-muted');
    if (welcomeMsg) {
        welcomeMsg.remove();
    }

    const messageDiv = document.createElement('div');
    messageDiv.id = messageId;
    messageDiv.className = `mb-3 ${role === 'user' ? 'text-end' : 'text-start'}`;

    let sourcesHtml = '';
    if (sources && sources.length > 0) {
        sourcesHtml = `
            <div class="mt-2">
                <small class="text-muted">Sources:</small>
                <div class="mt-1">
                    ${sources.map((source, index) => `
                        <span class="badge bg-secondary me-1">${index + 1}. ${source.title || 'Document'}</span>
                    `).join('')}
                </div>
            </div>
        `;
    }

    messageDiv.innerHTML = `
        <div class="d-inline-block p-3 rounded-3 ${role === 'user' ? 'bg-primary text-white' : 'bg-light'}" style="max-width: 80%;">
            <div class="d-flex align-items-start">
                <div class="me-2">
                    <i class="bi ${role === 'user' ? 'bi-person-fill' : 'bi-robot'}"></i>
                </div>
                <div class="flex-grow-1">
                    <div>${content}</div>
                    ${sourcesHtml}
                </div>
            </div>
        </div>
    `;

    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;

    // Add to chat history if not temporary
    if (!isTemporary) {
        chatHistory.push({ role, content, sources, timestamp: new Date().toISOString() });
        saveChatHistory();
    }

    return messageId;
}

function removeMessage(messageId) {
    const message = document.getElementById(messageId);
    if (message) {
        message.remove();
    }
}

function askQuestion(question) {
    document.getElementById('question-input').value = question;
    document.getElementById('question-input').focus();
}

function clearChat() {
    if (confirm('Are you sure you want to clear the chat history?')) {
        document.getElementById('chat-messages').innerHTML = `
            <div class="text-center text-muted py-5">
                <i class="bi bi-chat-square-dots fs-1 mb-3"></i>
                <h5>Start a conversation</h5>
                <p>Ask questions about your documents and knowledge graph</p>
            </div>
        `;
        chatHistory = [];
        saveChatHistory();
        showAlert('Chat cleared', 'info', 2000);
    }
}

function exportChat() {
    if (chatHistory.length === 0) {
        showAlert('No chat history to export', 'warning');
        return;
    }

    const exportData = {
        timestamp: new Date().toISOString(),
        model: currentModel,
        messages: chatHistory
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat-export-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);

    showAlert('Chat exported successfully', 'success', 2000);
}

function toggleSettings() {
    const panel = document.getElementById('settings-panel');
    panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
}

function saveChatHistory() {
    localStorage.setItem('graphiti-chat-history', JSON.stringify(chatHistory));
}

function loadChatHistory() {
    const saved = localStorage.getItem('graphiti-chat-history');
    if (saved) {
        try {
            chatHistory = JSON.parse(saved);

            // Restore messages
            chatHistory.forEach(msg => {
                addMessage(msg.role, msg.content, false, msg.sources);
            });
        } catch (error) {
            console.error('Error loading chat history:', error);
        }
    }
}
</script>
{% endblock %}
